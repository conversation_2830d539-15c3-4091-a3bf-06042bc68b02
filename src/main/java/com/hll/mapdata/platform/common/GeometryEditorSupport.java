package com.hll.mapdata.platform.common;

import io.micrometer.common.util.StringUtils;
import org.locationtech.jts.geom.Geometry;
import org.locationtech.jts.io.ParseException;
import org.locationtech.jts.io.WKTReader;

import java.beans.PropertyEditorSupport;

public class GeometryEditorSupport extends PropertyEditorSupport {

    private final boolean allowEmpty;

    public GeometryEditorSupport(boolean allowEmpty) {
        this.allowEmpty = allowEmpty;
    }

    @Override
    public void setAsText(String text) {
        if (StringUtils.isBlank(text)) {
            if (!allowEmpty) {
                throw new IllegalArgumentException("geometry cannot be null");
            }
            setValue(null);
            return;
        }
        WKTReader wktReader = new WKTReader();
        try {
            Geometry geometry = wktReader.read(text);
            setValue(geometry);
        } catch (ParseException e) {
            throw new IllegalArgumentException("Invalid geometry: " + text);
        }
    }
}
