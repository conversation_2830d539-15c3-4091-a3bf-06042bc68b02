package com.hll.mapdata.platform.service;

import com.uber.h3core.H3Core;
import com.uber.h3core.util.LatLng;
import org.locationtech.jts.geom.Coordinate;
import org.locationtech.jts.geom.Geometry;
import org.locationtech.jts.geom.GeometryFactory;
import org.locationtech.jts.geom.LineString;
import org.springframework.stereotype.Service;

import java.io.IOException;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

@Service
public class H3Service {

    private final static GeometryFactory geometryFactory = new GeometryFactory();

    /**
     * 根据传入的几何（WKT）求质心，再根据分辨率计算 H3 索引
     *
     * @param geometry   例如一个 Polygon 的 WKT
     * @param resolution 分辨率 (0 ~ 15), 例如 7
     * @return 返回 H3 hex string 索引，比如 "87411114affffff"
     */
    public String geometryToH3(Geometry geometry, int resolution) throws Exception {
        // 2. 获取质心坐标
        Coordinate centroidCoordinate = geometry.getCentroid().getCoordinate();
        double lon = centroidCoordinate.x; // 经度
        double lat = centroidCoordinate.y; // 纬度

        // 3. 计算 H3 索引
        H3Core h3 = H3Core.newInstance();
        return h3.latLngToCellAddress(lat, lon, resolution);

    }

    /**
     * 将单个 H3 索引转换为对应多边形的 WKT
     *
     * @param h3Index 例如 "87411114affffff"
     * @return 例如 "POLYGON((lon1 lat1, ..., lonN latN, lon1 lat1))"
     */
    public Geometry h3ToGeometry(String h3Index) throws IOException {
        // 1. 创建 H3Core 实例
        H3Core h3 = H3Core.newInstance();
        // 2. 获取此 H3 单元对应的多边形边界（经纬度坐标）
        List<LatLng> boundary = h3.cellToBoundary(h3Index);
        // 3. 组装成 POLYGON
        Coordinate[] coordinates = new Coordinate[boundary.size() + 1];
        LatLng latLng;
        for (int i = 0; i < boundary.size(); i++) {
            latLng = boundary.get(i);
            coordinates[i] = new Coordinate(latLng.lng, latLng.lat);
        }

        // 闭合多边形：补回首个点坐标
        LatLng first = boundary.get(0);
        coordinates[boundary.size()] = new Coordinate(first.lng, first.lat);
        return geometryFactory.createPolygon(coordinates);
    }

    public List<String> geometryToH3List(Geometry geometry, int resolution) throws Exception {
        H3Core h3 = H3Core.newInstance();
        Coordinate[] coords = geometry.getExteriorRing().getCoordinates();
        List<LatLng> outer = new ArrayList<>(coords.length);
        for (Coordinate c : coords) {
            outer.add(new LatLng(c.getY(), c.getX())); // LatLng(lat, lon)
        }
        List<List<LatLng>> geoPolygon = Collections.singletonList(outer);
        List<String> h3Ids = h3.polyfillAddress(geoPolygon, resolution);
        return h3Ids;
    }

    // 简单测试
    public static void main(String[] args) throws Exception {
        Coordinate[] coordinates = new Coordinate[2];
        coordinates[0] = new Coordinate(112.237509, 22.784975);
        coordinates[1] = new Coordinate(112.237045, 22.785753);
        LineString lineString = geometryFactory.createLineString(coordinates);
        H3Service h3Service = new H3Service();
        // 计算7级 H3
        String h3Index = h3Service.geometryToH3(lineString, 7);
        System.out.println("H3索引 = " + h3Index);
        // 计算对应的geometry
        Geometry geometry = h3Service.h3ToGeometry(h3Index);
        System.out.println("H3索引[" + h3Index + "]范围 = " + geometry);
    }
}

    