plugins {
    id 'java'
    id 'org.springframework.boot' version "${springBootVersion}"
    id 'io.spring.dependency-management' version "${springDependencyManager}"
}

group = "${appGroup}"
version = "${appVersion}"

repositories {
    maven {
        url 'http://maven.huolala.cn/repository/maven-public/'
        allowInsecureProtocol = true
    }
    maven { url 'https://maven.aliyun.com/repository/public/' }
    mavenCentral()
}

dependencies {
    implementation 'org.springframework.boot:spring-boot-starter-web'
    implementation "org.locationtech.jts:jts-core:${jtsVersion}"
    implementation "com.uber:h3:${uberVersion}"
    implementation "com.hll.map.boot:map-spring-core:${mapVersion}"

    testImplementation 'org.springframework.boot:spring-boot-starter-test'
    testRuntimeOnly 'org.junit.platform:junit-platform-launcher'
}

tasks.named('test') {
    useJUnitPlatform()
}
