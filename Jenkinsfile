pipeline {
    agent any
    options {
       timeout(time: 15, unit: 'MINUTES')
    }
    stages {
        stage('Clear dir') {
            steps {
                sh 'rm -rf build/libs'
            }
        }
        stage('Compile') {
            agent {
                docker {
                    image 'gradle:7-jdk17-alpine'
                    args '-v /data/gradle:/home/<USER>/.gradle'
                }
            }
            steps {
                sh 'gradle :clean :bootJar'
                stash includes: 'build/libs/*jar', name: 'app'
            }
        }
        stage('Build') {
            steps {
                unstash name: 'app'
                sh './gradlew jBuildImage'
            }
        }

        stage('Push') {
            steps {
                sh './gradlew pushImageForDev'
                sh './gradlew pushImageForPrd'
            }
        }

        stage('Deploy') {
            steps {
                sh './gradlew upgradeForDev'
                sh './gradlew restartForDev'
            }

            post {
                  success {
                    echo 'deploy success'
                  }

                  unstable {
                    echo 'I am unstable :/'
                  }

                  failure {
                    echo 'I failed :('
                  }

                  changed {
                    echo 'Things were different before...'
                  }
            }
        }
    }
}